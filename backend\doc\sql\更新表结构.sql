-- 2025-07-29 用户日程信息表
-- 用于存储用户的日程、用餐、住宿信息，给个人中心使用

DROP TABLE IF EXISTS "hy_user_schedule";
CREATE TABLE hy_user_schedule (
    id BIGINT PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                  -- 用户ID，关联blade_user表
    schedule_content TEXT,                    -- 日程信息（富文本）
    dining_info TEXT,                        -- 用餐信息（JSON格式）
    accommodation_info TEXT,                 -- 住宿信息（JSON格式）
    create_user BIGINT,                       -- 创建人
    create_dept BIGINT,                       -- 创建部门
    create_time TIMESTAMP(6),                 -- 创建时间
    update_user BIGINT,                       -- 更新人
    update_time TIMESTAMP(6),                 -- 更新时间
    status INT DEFAULT 1,                     -- 状态
    is_deleted INT DEFAULT 0                  -- 是否删除
);

-- 添加表注释
COMMENT ON TABLE hy_user_schedule IS '用户日程信息表';
COMMENT ON COLUMN hy_user_schedule.id IS '主键，自增';
COMMENT ON COLUMN hy_user_schedule.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_user_schedule.schedule_content IS '日程信息（富文本）';
COMMENT ON COLUMN hy_user_schedule.dining_info IS '用餐信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.accommodation_info IS '住宿信息（JSON格式）';
COMMENT ON COLUMN hy_user_schedule.create_user IS '创建人';
COMMENT ON COLUMN hy_user_schedule.create_dept IS '创建部门';
COMMENT ON COLUMN hy_user_schedule.create_time IS '创建时间';
COMMENT ON COLUMN hy_user_schedule.update_user IS '更新人';
COMMENT ON COLUMN hy_user_schedule.update_time IS '更新时间';
COMMENT ON COLUMN hy_user_schedule.status IS '状态';
COMMENT ON COLUMN hy_user_schedule.is_deleted IS '是否删除';

-- 创建索引
CREATE INDEX idx_hy_user_schedule_user_id ON hy_user_schedule(user_id);
CREATE INDEX idx_hy_user_schedule_status ON hy_user_schedule(status);
CREATE INDEX idx_hy_user_schedule_is_deleted ON hy_user_schedule(is_deleted);

-- 2025-07-30 为hy_sub_venue表添加视频URL和PDF URL字段
ALTER TABLE hy_sub_venue
    ADD COLUMN video_url VARCHAR(500),
    ADD COLUMN pdf_url VARCHAR(500);

COMMENT ON COLUMN hy_sub_venue.video_url IS '视频URL';
COMMENT ON COLUMN hy_sub_venue.pdf_url IS 'PDF文档URL';

-- 示例数据插入
INSERT INTO hy_user_schedule (user_id, schedule_content, dining_info, accommodation_info, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
(1123598821738675201,
 '<h3>我的会议日程</h3><p><strong>第一天（2024-07-29）</strong></p><ul><li>09:00-10:30 开幕式及主旨演讲</li><li>10:45-12:00 技术分享会</li><li>14:00-15:30 圆桌讨论</li><li>16:00-17:00 闭幕式</li></ul><p><strong>第二天（2024-07-30）</strong></p><ul><li>09:00-10:30 分会场讨论</li><li>10:45-12:00 成果展示</li></ul>',
 '{"breakfast": {"time": "07:30-09:00", "location": "酒店自助餐厅", "menu": ["中式早餐", "西式早餐", "粥类", "面包"], "dietary_requirements": "无特殊要求"}, "lunch": {"time": "12:00-13:30", "location": "会场餐厅", "menu": ["商务套餐A", "商务套餐B", "素食套餐"], "dietary_requirements": "无特殊要求"}, "dinner": {"time": "18:00-20:00", "location": "欢迎晚宴厅", "menu": ["中式宴席", "西式自助"], "dietary_requirements": "无特殊要求"}, "special_notes": "请提前30分钟到达用餐地点"}',
 '{"hotel_name": "国际大酒店", "room_type": "标准双人间", "check_in_date": "2024-07-28", "check_out_date": "2024-07-31", "room_number": "1208", "contact_phone": "************", "address": "市中心商务区会展路88号", "amenities": ["免费WiFi", "健身房", "游泳池", "商务中心"], "transportation": {"distance_to_venue": "500米", "shuttle_service": "提供免费班车", "shuttle_times": ["08:00", "08:30", "17:30", "18:00"]}, "special_services": ["24小时前台", "行李寄存", "叫醒服务"]}',
 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);
